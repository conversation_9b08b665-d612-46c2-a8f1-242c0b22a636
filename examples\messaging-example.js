import NeosendingClient from '../src/index.js';
import { generateReferenceId } from '../src/utils/index.js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

/**
 * Comprehensive messaging example
 */
async function messagingExample() {
  try {
    // Initialize the client
    const client = new NeosendingClient({
      username: process.env.NEOSENDING_USERNAME || 'your-username',
      password: process.env.NEOSENDING_PASSWORD || 'your-password'
    });

    console.log('📱 Neosending Messaging Example');
    console.log('================================\n');

    // Replace with actual phone numbers for testing
    const testReceiver = process.env.TEST_RECEIVER_MOBILE || '+1234567890';
    const testSender = process.env.TEST_SENDER_MOBILE || '+0987654321';

    console.log(`📞 Test receiver: ${testReceiver}`);
    console.log(`📞 Test sender: ${testSender}\n`);

    // 1. Send a simple text message
    console.log('1️⃣ Sending simple text message...');
    try {
      const textResult = await client.messages.sendTextMessage({
        receiverMobile: testReceiver,
        senderMobile: testSender,
        content: 'Hello! This is a test message from Neosending API Client 🚀',
        priority: 2,
        referenceId: generateReferenceId()
      });
      console.log('✅ Text message sent:', textResult.status);
      console.log('   Reference:', textResult.reference);
    } catch (error) {
      console.log('❌ Failed to send text message:', error.message);
    }

    // 2. Send a message with high priority
    console.log('\n2️⃣ Sending high priority message...');
    try {
      const priorityResult = await client.messages.sendTextMessage({
        receiverMobile: testReceiver,
        content: '🚨 URGENT: This is a high priority message!',
        priority: 4, // Urgent priority
        referenceId: generateReferenceId()
      });
      console.log('✅ Priority message sent:', priorityResult.status);
    } catch (error) {
      console.log('❌ Failed to send priority message:', error.message);
    }

    // 3. Send a file message (example with a text file)
    console.log('\n3️⃣ Sending file message...');
    try {
      // Create a sample text file
      const sampleContent = 'This is a sample document created by Neosending API Client.\n\nFeatures:\n- Easy to use\n- Comprehensive API\n- Great documentation';
      const fileBuffer = Buffer.from(sampleContent, 'utf8');

      const fileResult = await client.messages.sendFileMessage({
        receiverMobile: testReceiver,
        content: fileBuffer,
        fileName: 'sample-document.txt',
        caption: '📄 Here is your sample document',
        priority: 2,
        referenceId: generateReferenceId()
      });
      console.log('✅ File message sent:', fileResult.status);
    } catch (error) {
      console.log('❌ Failed to send file message:', error.message);
    }

    // 4. Send a vCard (contact card)
    console.log('\n4️⃣ Sending vCard...');
    try {
      const vCardResult = await client.messages.sendVCard({
        receiverMobile: testReceiver,
        contactName: 'John Doe',
        contactId: '+1555123456',
        isGroup: false
      });
      console.log('✅ vCard sent:', vCardResult.status);
    } catch (error) {
      console.log('❌ Failed to send vCard:', error.message);
    }

    // 5. Send templated image message
    console.log('\n5️⃣ Sending templated image message...');
    try {
      const templatedResult = await client.messages.sendTemplatedImageMessage({
        receiverMobile: testReceiver,
        templateContent: 'Welcome to our service! {{name}}',
        caption: '🎉 Welcome message with template',
        fileName: 'welcome-image.jpg',
        priority: 2
      });
      console.log('✅ Templated image sent:', templatedResult.status);
    } catch (error) {
      console.log('❌ Failed to send templated image:', error.message);
    }

    // 6. Batch messaging example
    console.log('\n6️⃣ Sending batch messages...');
    try {
      const batchMessages = [
        client.messages.createTextMessage(testReceiver, '📋 Batch message 1: Welcome!'),
        client.messages.createTextMessage(testReceiver, '📋 Batch message 2: Getting started guide'),
        client.messages.createTextMessage(testReceiver, '📋 Batch message 3: Thank you for using our service!')
      ];

      const batchResults = await client.messages.sendBatchMessages(batchMessages);
      console.log(`✅ Batch messages processed: ${batchResults.length}`);
      
      batchResults.forEach((result, index) => {
        if (result.error) {
          console.log(`   Message ${index + 1}: ❌ ${result.message}`);
        } else {
          console.log(`   Message ${index + 1}: ✅ ${result.status}`);
        }
      });
    } catch (error) {
      console.log('❌ Failed to send batch messages:', error.message);
    }

    // 7. Message with reply
    console.log('\n7️⃣ Sending reply message...');
    try {
      const replyResult = await client.messages.sendTextMessage({
        receiverMobile: testReceiver,
        content: '↩️ This is a reply to your previous message',
        whatsappReplyMessageId: 'some-message-id', // Replace with actual message ID
        referenceId: generateReferenceId()
      });
      console.log('✅ Reply message sent:', replyResult.status);
    } catch (error) {
      console.log('❌ Failed to send reply message:', error.message);
    }

    console.log('\n🎉 Messaging example completed!');
    console.log('\n💡 Tips:');
    console.log('   - Always use valid phone numbers in international format');
    console.log('   - Check message status and references for tracking');
    console.log('   - Use appropriate priority levels for different message types');
    console.log('   - Handle errors gracefully in production code');

  } catch (error) {
    console.error('❌ Error in messaging example:', error.message);
    if (error.statusCode) {
      console.error(`Status Code: ${error.statusCode}`);
    }
  }
}

// Helper function to create a sample image buffer (for demonstration)
function createSampleImageBuffer() {
  // This is a minimal 1x1 pixel PNG in base64
  const base64PNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
  return Buffer.from(base64PNG, 'base64');
}

// Run the example
if (import.meta.url === `file://${process.argv[1]}`) {
  messagingExample();
}

export default messagingExample;
