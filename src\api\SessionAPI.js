import { ValidationError } from '../errors/index.js';

/**
 * Session API methods for managing WhatsApp sessions
 */
class SessionAPI {
  constructor(client) {
    this.client = client;
  }

  /**
   * Start a new WhatsApp session
   * @param {Object} sessionData - Session configuration
   * @param {string} sessionData.mobileNumber - Mobile number for the session
   * @param {string} sessionData.webhook - Webhook URL for receiving events (optional)
   * @param {boolean} sessionData.waitQrCode - Whether to wait for QR code (optional)
   * @returns {Promise<Object>} Session start response with status and QR code
   */
  async startSession(sessionData) {
    if (!sessionData.mobileNumber) {
      throw new ValidationError('mobileNumber is required');
    }

    const response = await this.client.http.post('/api/neosending/Whatsapp/session/start-session', sessionData);
    return response.data;
  }

  /**
   * Get the current session status
   * @param {string} mobileNumber - Mobile number to check status for
   * @returns {Promise<Object>} Session status with QR code, version, and message
   */
  async getSessionStatus(mobileNumber) {
    if (!mobileNumber) {
      throw new ValidationError('mobileNumber is required');
    }

    const response = await this.client.http.get(`/api/neosending/Whatsapp/session/status-session/${mobileNumber}`);
    return response.data;
  }

  /**
   * Get QR code for session authentication
   * @param {string} mobileNumber - Mobile number to get QR code for
   * @returns {Promise<Object>} QR code response with image and content
   */
  async getQRCode(mobileNumber) {
    if (!mobileNumber) {
      throw new ValidationError('mobileNumber is required');
    }

    const response = await this.client.http.get(`/api/neosending/Whatsapp/session/qrcode-session/${mobileNumber}`);
    return response.data;
  }

  /**
   * Check session connection status
   * @param {string} mobileNumber - Mobile number to check connection for
   * @returns {Promise<Object>} Connection status response
   */
  async checkConnection(mobileNumber) {
    if (!mobileNumber) {
      throw new ValidationError('mobileNumber is required');
    }

    const response = await this.client.http.get(`/api/neosending/Whatsapp/session/check-connection-session/${mobileNumber}`);
    return response.data;
  }

  /**
   * Logout from a session
   * @param {string} mobileNumber - Mobile number to logout
   * @returns {Promise<Object>} Logout response
   */
  async logoutSession(mobileNumber) {
    if (!mobileNumber) {
      throw new ValidationError('mobileNumber is required');
    }

    const response = await this.client.http.post(`/api/neosending/Whatsapp/session/logout-session/${mobileNumber}`);
    return response.data;
  }

  /**
   * Close a session
   * @param {string} mobileNumber - Mobile number to close session for
   * @returns {Promise<Object>} Close session response
   */
  async closeSession(mobileNumber) {
    if (!mobileNumber) {
      throw new ValidationError('mobileNumber is required');
    }

    const response = await this.client.http.post(`/api/neosending/Whatsapp/session/close-session/${mobileNumber}`);
    return response.data;
  }

  /**
   * Delete a session
   * @param {Object} deleteData - Delete session data
   * @param {string} deleteData.mobileNumber - Mobile number to delete session for
   * @returns {Promise<Object>} Delete session response
   */
  async deleteSession(deleteData) {
    if (!deleteData.mobileNumber) {
      throw new ValidationError('mobileNumber is required');
    }

    const response = await this.client.http.post('/api/neosending/Whatsapp/session/delete-session', deleteData);
    return response.data;
  }

  /**
   * Restart a session
   * @param {Object} restartData - Restart session data
   * @param {string} restartData.mobileNumber - Mobile number to restart session for
   * @returns {Promise<Object>} Restart session response
   */
  async restartSession(restartData) {
    if (!restartData.mobileNumber) {
      throw new ValidationError('mobileNumber is required');
    }

    const response = await this.client.http.post('/api/neosending/Whatsapp/session/restart-session', restartData);
    return response.data;
  }

  /**
   * Take a screenshot of the session
   * @param {string} mobileNumber - Mobile number to take screenshot for
   * @returns {Promise<Object>} Screenshot response with base64 image
   */
  async takeScreenshot(mobileNumber) {
    if (!mobileNumber) {
      throw new ValidationError('mobileNumber is required');
    }

    const response = await this.client.http.get(`/api/neosending/Whatsapp/session/screenshot-session/${mobileNumber}`);
    return response.data;
  }

  /**
   * Get WhatsApp groups for a session
   * @param {string} mobileNumber - Mobile number to get groups for
   * @returns {Promise<Object>} Groups response with list of groups
   */
  async getWhatsappGroups(mobileNumber) {
    if (!mobileNumber) {
      throw new ValidationError('mobileNumber is required');
    }

    const response = await this.client.http.get(`/api/neosending/Whatsapp/session/whatsapp-groups/${mobileNumber}`);
    return response.data;
  }

  /**
   * Start Node.js server for a session
   * @param {Object} serverData - Server start data
   * @param {string} serverData.mobileNumber - Mobile number to start server for
   * @returns {Promise<Object>} Server start response
   */
  async startNodeJsServer(serverData) {
    if (!serverData.mobileNumber) {
      throw new ValidationError('mobileNumber is required');
    }

    const response = await this.client.http.post('/api/neosending/Whatsapp/session/start-nodejs-server', serverData);
    return response.data;
  }

  /**
   * Generate token for WppConnect
   * @param {string} session - Session name
   * @returns {Promise<Object>} Token generation response
   */
  async generateWppConnectToken(session) {
    if (!session) {
      throw new ValidationError('session is required');
    }

    const response = await this.client.http.post(`/api/neosending/Whatsapp/session/generate-token/${session}`);
    return response.data;
  }

  /**
   * Wait for session to be ready (polling method)
   * @param {string} mobileNumber - Mobile number to wait for
   * @param {number} maxAttempts - Maximum number of attempts (default: 30)
   * @param {number} intervalMs - Interval between checks in milliseconds (default: 2000)
   * @returns {Promise<Object>} Final session status
   */
  async waitForSessionReady(mobileNumber, maxAttempts = 30, intervalMs = 2000) {
    if (!mobileNumber) {
      throw new ValidationError('mobileNumber is required');
    }

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const status = await this.getSessionStatus(mobileNumber);
      
      if (status.status === 'CONNECTED' || status.status === 'READY') {
        return status;
      }
      
      if (status.status === 'FAILED' || status.status === 'DISCONNECTED') {
        throw new Error(`Session failed to connect: ${status.message}`);
      }

      // Wait before next attempt
      await new Promise(resolve => setTimeout(resolve, intervalMs));
    }

    throw new Error(`Session did not become ready within ${maxAttempts} attempts`);
  }

  /**
   * Helper method to start session and wait for QR code
   * @param {string} mobileNumber - Mobile number
   * @param {string} webhook - Webhook URL (optional)
   * @returns {Promise<Object>} Session with QR code
   */
  async startSessionAndGetQR(mobileNumber, webhook = null) {
    const sessionResult = await this.startSession({
      mobileNumber,
      webhook,
      waitQrCode: true
    });

    if (sessionResult.qrcode) {
      return sessionResult;
    }

    // If no QR code in start response, try to get it separately
    const qrResult = await this.getQRCode(mobileNumber);
    return {
      ...sessionResult,
      qrcode: qrResult.image || qrResult.content
    };
  }
}

export default SessionAPI;
