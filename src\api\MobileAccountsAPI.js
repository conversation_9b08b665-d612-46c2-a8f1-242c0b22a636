import { ValidationError } from '../errors/index.js';

/**
 * Mobile Accounts API methods for managing WhatsApp mobile accounts
 */
class MobileAccountsAPI {
  constructor(client) {
    this.client = client;
  }

  /**
   * Get a mobile account by ID
   * @param {number} id - Mobile account ID
   * @returns {Promise<Object>} Mobile account data
   */
  async getMobileAccount(id) {
    if (!id || typeof id !== 'number') {
      throw new ValidationError('Mobile account ID is required and must be a number');
    }

    const response = await this.client.http.get(`/api/neosending/Whatsapp/mobile-account/${id}`);
    return response.data;
  }

  /**
   * Get list of mobile accounts with pagination
   * @param {Object} options - Query options
   * @param {string} options.sorting - Sorting criteria
   * @param {number} options.skipCount - Number of records to skip
   * @param {number} options.maxResultCount - Maximum number of records to return
   * @returns {Promise<Object>} Paginated mobile accounts list
   */
  async getMobileAccounts(options = {}) {
    const params = new URLSearchParams();
    
    if (options.sorting) params.append('Sorting', options.sorting);
    if (options.skipCount !== undefined) params.append('SkipCount', options.skipCount);
    if (options.maxResultCount !== undefined) params.append('MaxResultCount', options.maxResultCount);

    const response = await this.client.http.get(`/api/neosending/Whatsapp/mobile-account?${params}`);
    return response.data;
  }

  /**
   * Create or update a mobile account
   * @param {Object} accountData - Mobile account data
   * @param {number} accountData.id - Account ID (0 for create, existing ID for update)
   * @param {number} accountData.customerId - Customer ID (required)
   * @param {string} accountData.mobileNumber - Mobile number (max 250 chars)
   * @param {number} accountData.clientType - Client type (1, 2, or 3)
   * @returns {Promise<Object>} Created/updated mobile account data
   */
  async createOrUpdateMobileAccount(accountData) {
    // Validate required fields
    if (accountData.customerId === undefined || accountData.customerId === null) {
      throw new ValidationError('customerId is required');
    }
    if (typeof accountData.customerId !== 'number') {
      throw new ValidationError('customerId must be a number');
    }

    // Validate client type
    if (accountData.clientType && ![1, 2, 3].includes(accountData.clientType)) {
      throw new ValidationError('clientType must be 1, 2, or 3');
    }

    // Validate mobile number length
    if (accountData.mobileNumber && accountData.mobileNumber.length > 250) {
      throw new ValidationError('mobileNumber must not exceed 250 characters');
    }

    const response = await this.client.http.post('/api/neosending/Whatsapp/mobile-account', accountData);
    return response.data;
  }

  /**
   * Delete a mobile account
   * @param {number} id - Mobile account ID
   * @returns {Promise<void>}
   */
  async deleteMobileAccount(id) {
    if (!id || typeof id !== 'number') {
      throw new ValidationError('Mobile account ID is required and must be a number');
    }

    await this.client.http.delete(`/api/neosending/Whatsapp/mobile-account/${id}`);
  }

  /**
   * Get customer lookup data for mobile accounts
   * @returns {Promise<Array<Object>>} Array of customer lookup objects
   */
  async getCustomerLookup() {
    const response = await this.client.http.get('/api/neosending/Whatsapp/mobile-account/customer-lookup');
    return response.data;
  }

  /**
   * Get mobile accounts for a specific customer
   * @param {number} customerId - Customer ID
   * @returns {Promise<Array<Object>>} Array of mobile accounts for the customer
   */
  async getMobileAccountsByCustomer(customerId) {
    if (!customerId || typeof customerId !== 'number') {
      throw new ValidationError('Customer ID is required and must be a number');
    }

    const accounts = await this.getMobileAccounts();
    return accounts.items ? accounts.items.filter(account => account.customerId === customerId) : [];
  }

  /**
   * Create a new mobile account
   * @param {number} customerId - Customer ID
   * @param {string} mobileNumber - Mobile number
   * @param {number} clientType - Client type (1, 2, or 3)
   * @returns {Promise<Object>} Created mobile account data
   */
  async createMobileAccount(customerId, mobileNumber, clientType = 1) {
    return this.createOrUpdateMobileAccount({
      id: 0, // 0 indicates create
      customerId,
      mobileNumber,
      clientType
    });
  }

  /**
   * Update an existing mobile account
   * @param {number} id - Mobile account ID
   * @param {Object} updates - Fields to update
   * @returns {Promise<Object>} Updated mobile account data
   */
  async updateMobileAccount(id, updates) {
    if (!id || typeof id !== 'number') {
      throw new ValidationError('Mobile account ID is required and must be a number');
    }

    // Get current account data first
    const currentAccount = await this.getMobileAccount(id);
    
    return this.createOrUpdateMobileAccount({
      id,
      customerId: currentAccount.customerId,
      mobileNumber: currentAccount.mobileNumber,
      clientType: currentAccount.clientType,
      ...updates
    });
  }

  /**
   * Get mobile account types/client types
   * @returns {Object} Object mapping client type numbers to descriptions
   */
  getMobileAccountTypes() {
    return {
      1: 'Type 1',
      2: 'Type 2', 
      3: 'Type 3'
    };
  }

  /**
   * Validate mobile account data
   * @param {Object} accountData - Account data to validate
   * @returns {Object} Validation result with isValid boolean and errors array
   */
  validateMobileAccountData(accountData) {
    const errors = [];

    if (!accountData.customerId) {
      errors.push('customerId is required');
    } else if (typeof accountData.customerId !== 'number') {
      errors.push('customerId must be a number');
    }

    if (accountData.mobileNumber && accountData.mobileNumber.length > 250) {
      errors.push('mobileNumber must not exceed 250 characters');
    }

    if (accountData.clientType && ![1, 2, 3].includes(accountData.clientType)) {
      errors.push('clientType must be 1, 2, or 3');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export default MobileAccountsAPI;
