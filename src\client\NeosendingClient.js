import axios from 'axios';
import AuthManager from '../auth/AuthManager.js';
import CustomerAPI from '../api/CustomerAPI.js';
import MessagesAPI from '../api/MessagesAPI.js';
import MobileAccountsAPI from '../api/MobileAccountsAPI.js';
import SessionAP<PERSON> from '../api/SessionAPI.js';
import PackagesAPI from '../api/PackagesAPI.js';
import SubscriptionsAPI from '../api/SubscriptionsAPI.js';
import { NeosendingError, NetworkError } from '../errors/index.js';

/**
 * Main client class for Neosending WhatsApp API
 */
class NeosendingClient {
  /**
   * Create a new Neosending client instance
   * @param {Object} config - Configuration object
   * @param {string} config.baseURL - Base URL for the API (default: https://neosending.com)
   * @param {string} config.username - Username for authentication
   * @param {string} config.password - Password for authentication
   * @param {number} config.timeout - Request timeout in milliseconds (default: 30000)
   * @param {Object} config.headers - Additional headers to include in requests
   */
  constructor(config = {}) {
    this.config = {
      baseURL: 'https://neosending.com',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      ...config
    };

    // Validate required config
    if (!this.config.username || !this.config.password) {
      throw new NeosendingError('Username and password are required');
    }

    // Create axios instance
    this.http = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: this.config.headers
    });

    // Initialize auth manager
    this.auth = new AuthManager(this);

    // Initialize API modules
    this.customers = new CustomerAPI(this);
    this.messages = new MessagesAPI(this);
    this.mobileAccounts = new MobileAccountsAPI(this);
    this.sessions = new SessionAPI(this);
    this.packages = new PackagesAPI(this);
    this.subscriptions = new SubscriptionsAPI(this);

    // Setup request/response interceptors
    this._setupInterceptors();
  }

  /**
   * Setup axios interceptors for authentication and error handling
   * @private
   */
  _setupInterceptors() {
    // Request interceptor - add auth token
    this.http.interceptors.request.use(
      async (config) => {
        const token = await this.auth.getValidToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(new NetworkError('Request setup failed', error));
      }
    );

    // Response interceptor - handle errors
    this.http.interceptors.response.use(
      (response) => response,
      async (error) => {
        // Handle 401 errors by refreshing token
        if (error.response?.status === 401) {
          try {
            await this.auth.refreshToken();
            // Retry the original request
            const originalRequest = error.config;
            const token = await this.auth.getValidToken();
            if (token) {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return this.http.request(originalRequest);
            }
          } catch (refreshError) {
            // If refresh fails, let the original error through
          }
        }

        // Transform axios errors to our custom errors
        if (error.response) {
          throw new NeosendingError(
            error.response.data?.error?.message || 'API request failed',
            error.response.status,
            error.response.data
          );
        } else if (error.request) {
          throw new NetworkError('Network request failed', error);
        } else {
          throw new NeosendingError('Request setup failed', null, error);
        }
      }
    );
  }

  /**
   * Test the connection to the API
   * @returns {Promise<boolean>} True if connection is successful
   */
  async testConnection() {
    try {
      await this.customers.getWhatsappCustomer();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get current authentication status
   * @returns {Promise<boolean>} True if authenticated
   */
  async isAuthenticated() {
    return this.auth.isAuthenticated();
  }

  /**
   * Manually refresh the authentication token
   * @returns {Promise<void>}
   */
  async refreshAuth() {
    return this.auth.refreshToken();
  }
}

export default NeosendingClient;
