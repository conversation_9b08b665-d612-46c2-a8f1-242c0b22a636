import NeosendingClient from '../src/index.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Basic usage example for Neosending WhatsApp API Client
 */
async function basicUsageExample() {
  try {
    // Initialize the client
    const client = new NeosendingClient({
      username: process.env.NEOSENDING_USERNAME || 'your-username',
      password: process.env.NEOSENDING_PASSWORD || 'your-password',
      baseURL: process.env.NEOSENDING_BASE_URL || 'https://neosending.com'
    });

    console.log('🚀 Neosending Client initialized');

    // Test connection
    console.log('🔍 Testing connection...');
    const isConnected = await client.testConnection();
    console.log(`✅ Connection test: ${isConnected ? 'SUCCESS' : 'FAILED'}`);

    if (!isConnected) {
      console.log('❌ Cannot proceed without valid connection');
      return;
    }

    // Get current customer information
    console.log('\n👤 Getting customer information...');
    try {
      const customer = await client.customers.getWhatsappCustomer();
      console.log('Customer info:', {
        id: customer.id,
        fullName: customer.fullName,
        companyName: customer.companyName,
        mobileNumber: customer.mobileNumber
      });
    } catch (error) {
      console.log('⚠️ Could not get customer info:', error.message);
    }

    // Get customer balance
    console.log('\n💰 Getting customer balance...');
    try {
      const balance = await client.customers.getCustomerBalance();
      console.log('Balance info:', {
        balance: balance.balance,
        expiryDate: balance.expiryDate
      });
    } catch (error) {
      console.log('⚠️ Could not get balance info:', error.message);
    }

    // Example: Send a text message (uncomment and modify as needed)
    /*
    console.log('\n📱 Sending text message...');
    const messageResult = await client.messages.sendTextMessage({
      receiverMobile: '+**********', // Replace with actual number
      content: 'Hello from Neosending API Client! 🎉',
      priority: 2
    });
    console.log('Message sent:', messageResult);
    */

    // Example: Get mobile accounts
    console.log('\n📱 Getting mobile accounts...');
    try {
      const accounts = await client.mobileAccounts.getMobileAccounts({
        maxResultCount: 5
      });
      console.log(`Found ${accounts.totalCount || 0} mobile accounts`);
      if (accounts.items && accounts.items.length > 0) {
        accounts.items.forEach((account, index) => {
          console.log(`  ${index + 1}. ${account.mobileNumber} (Type: ${account.clientType})`);
        });
      }
    } catch (error) {
      console.log('⚠️ Could not get mobile accounts:', error.message);
    }

    // Example: Get packages
    console.log('\n📦 Getting available packages...');
    try {
      const packages = await client.packages.getPackages({
        maxResultCount: 5
      });
      console.log(`Found ${packages.totalCount || 0} packages`);
      if (packages.items && packages.items.length > 0) {
        packages.items.forEach((pkg, index) => {
          console.log(`  ${index + 1}. ${pkg.name} - $${pkg.price} (${pkg.quantity} messages)`);
        });
      }
    } catch (error) {
      console.log('⚠️ Could not get packages:', error.message);
    }

    console.log('\n✅ Basic usage example completed successfully!');

  } catch (error) {
    console.error('❌ Error in basic usage example:', error.message);
    if (error.statusCode) {
      console.error(`Status Code: ${error.statusCode}`);
    }
    if (error.data) {
      console.error('Error Data:', error.data);
    }
  }
}

// Run the example
if (import.meta.url === `file://${process.argv[1]}`) {
  basicUsageExample();
}

export default basicUsageExample;
