/**
 * Configuration constants and helpers for the Neosending client
 */

/**
 * Default configuration values
 */
export const DEFAULT_CONFIG = {
  baseURL: 'https://neosending.com',
  timeout: 30000,
  maxRetries: 3,
  retryDelay: 1000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': 'neosending-client/1.0.0'
  }
};

/**
 * API endpoints
 */
export const ENDPOINTS = {
  // Authentication
  TOKEN: '/connect/token',
  
  // Customer endpoints
  CUSTOMERS: '/api/neosending/Whatsapp/customer',
  CUSTOMER_BY_ID: (id) => `/api/neosending/Whatsapp/customer/${id}`,
  WHATSAPP_CUSTOMER: '/api/neosending/Whatsapp/customer/whatsapp-customer',
  CUSTOMER_BALANCE: '/api/neosending/Whatsapp/customer/customer-balance',
  SAVE_BON_SETTINGS: '/api/neosending/Whatsapp/customer/save-bon-settings',
  
  // Messages endpoints
  SEND_TEXT_MESSAGE: '/api/neosending/Whatsapp/message/send-text',
  SEND_FILE_MESSAGE: '/api/neosending/Whatsapp/message/send-file',
  SEND_TEMPLATED_IMAGE: '/api/neosending/Whatsapp/message/send-templated-image',
  SEND_VCARD: '/api/neosending/Whatsapp/message/send-vcard',
  
  // Mobile Accounts endpoints
  MOBILE_ACCOUNTS: '/api/neosending/Whatsapp/mobile-account',
  MOBILE_ACCOUNT_BY_ID: (id) => `/api/neosending/Whatsapp/mobile-account/${id}`,
  
  // Session endpoints
  START_SESSION: '/api/neosending/Whatsapp/session/start-session',
  SESSION_STATUS: (mobileNumber) => `/api/neosending/Whatsapp/session/status-session/${mobileNumber}`,
  QR_CODE: (mobileNumber) => `/api/neosending/Whatsapp/session/qrcode-session/${mobileNumber}`,
  CHECK_CONNECTION: (mobileNumber) => `/api/neosending/Whatsapp/session/check-connection-session/${mobileNumber}`,
  LOGOUT_SESSION: (mobileNumber) => `/api/neosending/Whatsapp/session/logout-session/${mobileNumber}`,
  CLOSE_SESSION: (mobileNumber) => `/api/neosending/Whatsapp/session/close-session/${mobileNumber}`,
  DELETE_SESSION: '/api/neosending/Whatsapp/session/delete-session',
  RESTART_SESSION: '/api/neosending/Whatsapp/session/restart-session',
  SCREENSHOT: (mobileNumber) => `/api/neosending/Whatsapp/session/screenshot-session/${mobileNumber}`,
  WHATSAPP_GROUPS: (mobileNumber) => `/api/neosending/Whatsapp/session/whatsapp-groups/${mobileNumber}`,
  START_NODEJS_SERVER: '/api/neosending/Whatsapp/session/start-nodejs-server',
  GENERATE_TOKEN: (session) => `/api/neosending/Whatsapp/session/generate-token/${session}`,
  
  // Packages endpoints
  PACKAGES: '/api/neosending/Whatsapp/package',
  PACKAGE_BY_ID: (id) => `/api/neosending/Whatsapp/package/${id}`,
  
  // Subscriptions endpoints
  SUBSCRIPTIONS: '/api/neosending/Whatsapp/customer-subscription',
  SUBSCRIPTION_BY_ID: (id) => `/api/neosending/Whatsapp/customer-subscription/${id}`,
  CURRENT_PACKAGE: (customerId) => `/api/neosending/Whatsapp/customer-subscription/current-package/${customerId}`
};

/**
 * HTTP status codes
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504
};

/**
 * Mobile account types
 */
export const MOBILE_ACCOUNT_TYPES = {
  TYPE_1: 1,
  TYPE_2: 2,
  TYPE_3: 3
};

/**
 * Subscription period types
 */
export const SUBSCRIPTION_PERIOD_TYPES = {
  MONTHLY: 1,
  QUARTERLY: 2,
  SEMI_ANNUAL: 3,
  ANNUAL: 4
};

/**
 * Message priorities
 */
export const MESSAGE_PRIORITIES = {
  LOW: 1,
  NORMAL: 2,
  HIGH: 3,
  URGENT: 4
};

/**
 * Session statuses
 */
export const SESSION_STATUSES = {
  DISCONNECTED: 'DISCONNECTED',
  CONNECTING: 'CONNECTING',
  CONNECTED: 'CONNECTED',
  READY: 'READY',
  FAILED: 'FAILED',
  QRCODE: 'QRCODE'
};

/**
 * Validate client configuration
 * @param {Object} config - Configuration object
 * @returns {Object} Validated configuration
 */
export function validateConfig(config) {
  const validated = { ...DEFAULT_CONFIG, ...config };
  
  // Validate required fields
  if (!validated.username) {
    throw new Error('Username is required in configuration');
  }
  
  if (!validated.password) {
    throw new Error('Password is required in configuration');
  }
  
  // Validate baseURL
  if (!validated.baseURL || typeof validated.baseURL !== 'string') {
    throw new Error('baseURL must be a valid string');
  }
  
  // Ensure baseURL doesn't end with slash
  if (validated.baseURL.endsWith('/')) {
    validated.baseURL = validated.baseURL.slice(0, -1);
  }
  
  // Validate timeout
  if (validated.timeout && (typeof validated.timeout !== 'number' || validated.timeout <= 0)) {
    throw new Error('timeout must be a positive number');
  }
  
  // Validate maxRetries
  if (validated.maxRetries && (typeof validated.maxRetries !== 'number' || validated.maxRetries < 0)) {
    throw new Error('maxRetries must be a non-negative number');
  }
  
  return validated;
}

/**
 * Get environment-specific configuration
 * @param {string} environment - Environment name (development, staging, production)
 * @returns {Object} Environment configuration
 */
export function getEnvironmentConfig(environment = 'production') {
  const configs = {
    development: {
      baseURL: 'https://dev.neosending.com',
      timeout: 60000, // Longer timeout for development
      maxRetries: 1
    },
    staging: {
      baseURL: 'https://staging.neosending.com',
      timeout: 45000,
      maxRetries: 2
    },
    production: {
      baseURL: 'https://neosending.com',
      timeout: 30000,
      maxRetries: 3
    }
  };
  
  return configs[environment] || configs.production;
}

/**
 * Create configuration from environment variables
 * @returns {Object} Configuration object
 */
export function createConfigFromEnv() {
  return {
    baseURL: process.env.NEOSENDING_BASE_URL,
    username: process.env.NEOSENDING_USERNAME,
    password: process.env.NEOSENDING_PASSWORD,
    timeout: process.env.NEOSENDING_TIMEOUT ? parseInt(process.env.NEOSENDING_TIMEOUT) : undefined,
    maxRetries: process.env.NEOSENDING_MAX_RETRIES ? parseInt(process.env.NEOSENDING_MAX_RETRIES) : undefined
  };
}
