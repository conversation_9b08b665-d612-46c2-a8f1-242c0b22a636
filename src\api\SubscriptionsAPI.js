import { ValidationError } from '../errors/index.js';

/**
 * Customer Subscriptions API methods
 */
class SubscriptionsAPI {
  constructor(client) {
    this.client = client;
  }

  /**
   * Get a customer subscription by ID
   * @param {number} id - Subscription ID
   * @returns {Promise<Object>} Subscription data
   */
  async getSubscription(id) {
    if (!id || typeof id !== 'number') {
      throw new ValidationError('Subscription ID is required and must be a number');
    }

    const response = await this.client.http.get(`/api/neosending/Whatsapp/customer-subscription/${id}`);
    return response.data;
  }

  /**
   * Get list of customer subscriptions with pagination
   * @param {Object} options - Query options
   * @param {string} options.sorting - Sorting criteria
   * @param {number} options.skipCount - Number of records to skip
   * @param {number} options.maxResultCount - Maximum number of records to return
   * @returns {Promise<Object>} Paginated subscriptions list
   */
  async getSubscriptions(options = {}) {
    const params = new URLSearchParams();
    
    if (options.sorting) params.append('Sorting', options.sorting);
    if (options.skipCount !== undefined) params.append('SkipCount', options.skipCount);
    if (options.maxResultCount !== undefined) params.append('MaxResultCount', options.maxResultCount);

    const response = await this.client.http.get(`/api/neosending/Whatsapp/customer-subscription?${params}`);
    return response.data;
  }

  /**
   * Create or update a customer subscription
   * @param {Object} subscriptionData - Subscription data
   * @param {number} subscriptionData.id - Subscription ID (0 for create, existing ID for update)
   * @param {number} subscriptionData.customerId - Customer ID (required)
   * @param {string} subscriptionData.customerFullName - Customer full name (optional)
   * @param {number} subscriptionData.packageId - Package ID (required)
   * @param {string} subscriptionData.packageName - Package name (optional)
   * @param {number} subscriptionData.periodType - Period type (1=Monthly, 2=Quarterly, 3=Semi-Annual, 4=Annual)
   * @returns {Promise<Object>} Created/updated subscription data
   */
  async createOrUpdateSubscription(subscriptionData) {
    // Validate required fields
    if (subscriptionData.customerId === undefined || subscriptionData.customerId === null) {
      throw new ValidationError('customerId is required');
    }
    if (typeof subscriptionData.customerId !== 'number') {
      throw new ValidationError('customerId must be a number');
    }
    if (subscriptionData.packageId === undefined || subscriptionData.packageId === null) {
      throw new ValidationError('packageId is required');
    }
    if (typeof subscriptionData.packageId !== 'number') {
      throw new ValidationError('packageId must be a number');
    }

    // Validate period type
    if (subscriptionData.periodType && ![1, 2, 3, 4].includes(subscriptionData.periodType)) {
      throw new ValidationError('periodType must be 1 (Monthly), 2 (Quarterly), 3 (Semi-Annual), or 4 (Annual)');
    }

    const response = await this.client.http.post('/api/neosending/Whatsapp/customer-subscription', subscriptionData);
    return response.data;
  }

  /**
   * Create a new customer subscription
   * @param {number} customerId - Customer ID
   * @param {number} packageId - Package ID
   * @param {number} periodType - Period type (1=Monthly, 2=Quarterly, 3=Semi-Annual, 4=Annual)
   * @param {Object} additionalData - Additional subscription data
   * @returns {Promise<Object>} Created subscription data
   */
  async createSubscription(customerId, packageId, periodType = 1, additionalData = {}) {
    return this.createOrUpdateSubscription({
      id: 0, // 0 indicates create
      customerId,
      packageId,
      periodType,
      ...additionalData
    });
  }

  /**
   * Update an existing customer subscription
   * @param {number} id - Subscription ID
   * @param {Object} updates - Fields to update
   * @returns {Promise<Object>} Updated subscription data
   */
  async updateSubscription(id, updates) {
    if (!id || typeof id !== 'number') {
      throw new ValidationError('Subscription ID is required and must be a number');
    }

    // Get current subscription data first
    const currentSubscription = await this.getSubscription(id);
    
    return this.createOrUpdateSubscription({
      id,
      customerId: currentSubscription.customerId,
      packageId: currentSubscription.packageId,
      periodType: currentSubscription.periodType,
      ...updates
    });
  }

  /**
   * Get customer lookup data for subscriptions
   * @returns {Promise<Array<Object>>} Array of customer lookup objects
   */
  async getCustomerLookup() {
    const response = await this.client.http.get('/api/neosending/Whatsapp/customer-subscription/customer-lookup');
    return response.data;
  }

  /**
   * Get package lookup data for subscriptions
   * @returns {Promise<Array<Object>>} Array of package lookup objects
   */
  async getPackageLookup() {
    const response = await this.client.http.get('/api/neosending/Whatsapp/customer-subscription/package-lookup');
    return response.data;
  }

  /**
   * Get current package for a customer
   * @param {number} customerId - Customer ID
   * @returns {Promise<Object>} Current package information
   */
  async getCurrentPackage(customerId) {
    if (!customerId || typeof customerId !== 'number') {
      throw new ValidationError('Customer ID is required and must be a number');
    }

    const response = await this.client.http.get(`/api/neosending/Whatsapp/customer-subscription/current-package/${customerId}`);
    return response.data;
  }

  /**
   * Get subscriptions for a specific customer
   * @param {number} customerId - Customer ID
   * @returns {Promise<Array<Object>>} Array of subscriptions for the customer
   */
  async getSubscriptionsByCustomer(customerId) {
    if (!customerId || typeof customerId !== 'number') {
      throw new ValidationError('Customer ID is required and must be a number');
    }

    const subscriptions = await this.getSubscriptions();
    return subscriptions.items ? subscriptions.items.filter(sub => sub.customerId === customerId) : [];
  }

  /**
   * Get subscriptions by package
   * @param {number} packageId - Package ID
   * @returns {Promise<Array<Object>>} Array of subscriptions for the package
   */
  async getSubscriptionsByPackage(packageId) {
    if (!packageId || typeof packageId !== 'number') {
      throw new ValidationError('Package ID is required and must be a number');
    }

    const subscriptions = await this.getSubscriptions();
    return subscriptions.items ? subscriptions.items.filter(sub => sub.packageId === packageId) : [];
  }

  /**
   * Get subscription period types
   * @returns {Object} Object mapping period type numbers to descriptions
   */
  getSubscriptionPeriodTypes() {
    return {
      1: 'Monthly',
      2: 'Quarterly',
      3: 'Semi-Annual',
      4: 'Annual'
    };
  }

  /**
   * Calculate subscription end date based on period type
   * @param {Date} startDate - Subscription start date
   * @param {number} periodType - Period type (1-4)
   * @returns {Date} Calculated end date
   */
  calculateSubscriptionEndDate(startDate, periodType) {
    const endDate = new Date(startDate);
    
    switch (periodType) {
      case 1: // Monthly
        endDate.setMonth(endDate.getMonth() + 1);
        break;
      case 2: // Quarterly
        endDate.setMonth(endDate.getMonth() + 3);
        break;
      case 3: // Semi-Annual
        endDate.setMonth(endDate.getMonth() + 6);
        break;
      case 4: // Annual
        endDate.setFullYear(endDate.getFullYear() + 1);
        break;
      default:
        throw new ValidationError('Invalid period type');
    }
    
    return endDate;
  }

  /**
   * Validate subscription data
   * @param {Object} subscriptionData - Subscription data to validate
   * @returns {Object} Validation result with isValid boolean and errors array
   */
  validateSubscriptionData(subscriptionData) {
    const errors = [];

    if (!subscriptionData.customerId) {
      errors.push('customerId is required');
    } else if (typeof subscriptionData.customerId !== 'number') {
      errors.push('customerId must be a number');
    }

    if (!subscriptionData.packageId) {
      errors.push('packageId is required');
    } else if (typeof subscriptionData.packageId !== 'number') {
      errors.push('packageId must be a number');
    }

    if (subscriptionData.periodType && ![1, 2, 3, 4].includes(subscriptionData.periodType)) {
      errors.push('periodType must be 1 (Monthly), 2 (Quarterly), 3 (Semi-Annual), or 4 (Annual)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export default SubscriptionsAPI;
