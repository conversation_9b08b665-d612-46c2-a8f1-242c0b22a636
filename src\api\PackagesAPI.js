import { ValidationError } from '../errors/index.js';

/**
 * Packages API methods for managing WhatsApp packages
 */
class PackagesAPI {
  constructor(client) {
    this.client = client;
  }

  /**
   * Get a package by ID
   * @param {number} id - Package ID
   * @returns {Promise<Object>} Package data
   */
  async getPackage(id) {
    if (!id || typeof id !== 'number') {
      throw new ValidationError('Package ID is required and must be a number');
    }

    const response = await this.client.http.get(`/api/neosending/Whatsapp/package/${id}`);
    return response.data;
  }

  /**
   * Get list of packages with pagination
   * @param {Object} options - Query options
   * @param {string} options.sorting - Sorting criteria
   * @param {number} options.skipCount - Number of records to skip
   * @param {number} options.maxResultCount - Maximum number of records to return
   * @returns {Promise<Object>} Paginated packages list
   */
  async getPackages(options = {}) {
    const params = new URLSearchParams();
    
    if (options.sorting) params.append('Sorting', options.sorting);
    if (options.skipCount !== undefined) params.append('SkipCount', options.skipCount);
    if (options.maxResultCount !== undefined) params.append('MaxResultCount', options.maxResultCount);

    const response = await this.client.http.get(`/api/neosending/Whatsapp/package?${params}`);
    return response.data;
  }

  /**
   * Create a new package
   * @param {Object} packageData - Package data
   * @param {string} packageData.name - Package name (required, max 100 chars)
   * @param {number} packageData.quantity - Package quantity (required)
   * @param {number} packageData.price - Package price (required)
   * @param {string} packageData.features - Package features description (required, max 500 chars)
   * @param {boolean} packageData.unlimited - Whether package is unlimited (optional)
   * @returns {Promise<Object>} Created package data
   */
  async createPackage(packageData) {
    // Validate required fields
    if (!packageData.name) {
      throw new ValidationError('name is required');
    }
    if (packageData.name.length > 100) {
      throw new ValidationError('name must not exceed 100 characters');
    }
    if (packageData.quantity === undefined || packageData.quantity === null) {
      throw new ValidationError('quantity is required');
    }
    if (typeof packageData.quantity !== 'number') {
      throw new ValidationError('quantity must be a number');
    }
    if (packageData.price === undefined || packageData.price === null) {
      throw new ValidationError('price is required');
    }
    if (typeof packageData.price !== 'number') {
      throw new ValidationError('price must be a number');
    }
    if (!packageData.features) {
      throw new ValidationError('features is required');
    }
    if (packageData.features.length > 500) {
      throw new ValidationError('features must not exceed 500 characters');
    }

    const response = await this.client.http.post('/api/neosending/Whatsapp/package', packageData);
    return response.data;
  }

  /**
   * Update an existing package
   * @param {number} id - Package ID
   * @param {Object} packageData - Updated package data
   * @param {string} packageData.name - Package name (required, max 100 chars)
   * @param {number} packageData.quantity - Package quantity (required)
   * @param {number} packageData.price - Package price (required)
   * @param {string} packageData.features - Package features description (required, max 500 chars)
   * @param {boolean} packageData.unlimited - Whether package is unlimited (optional)
   * @returns {Promise<Object>} Updated package data
   */
  async updatePackage(id, packageData) {
    if (!id || typeof id !== 'number') {
      throw new ValidationError('Package ID is required and must be a number');
    }

    // Include ID in the data for update
    const updateData = { ...packageData, id };

    // Validate required fields (same as create)
    if (!updateData.name) {
      throw new ValidationError('name is required');
    }
    if (updateData.name.length > 100) {
      throw new ValidationError('name must not exceed 100 characters');
    }
    if (updateData.quantity === undefined || updateData.quantity === null) {
      throw new ValidationError('quantity is required');
    }
    if (typeof updateData.quantity !== 'number') {
      throw new ValidationError('quantity must be a number');
    }
    if (updateData.price === undefined || updateData.price === null) {
      throw new ValidationError('price is required');
    }
    if (typeof updateData.price !== 'number') {
      throw new ValidationError('price must be a number');
    }
    if (!updateData.features) {
      throw new ValidationError('features is required');
    }
    if (updateData.features.length > 500) {
      throw new ValidationError('features must not exceed 500 characters');
    }

    const response = await this.client.http.put(`/api/neosending/Whatsapp/package/${id}`, updateData);
    return response.data;
  }

  /**
   * Delete a package
   * @param {number} id - Package ID
   * @returns {Promise<void>}
   */
  async deletePackage(id) {
    if (!id || typeof id !== 'number') {
      throw new ValidationError('Package ID is required and must be a number');
    }

    await this.client.http.delete(`/api/neosending/Whatsapp/package/${id}`);
  }

  /**
   * Get package lookup data (simplified package list for dropdowns)
   * @returns {Promise<Array<Object>>} Array of package lookup objects
   */
  async getPackageLookup() {
    const response = await this.client.http.get('/api/neosending/Whatsapp/package/lookup');
    return response.data;
  }

  /**
   * Search packages by name
   * @param {string} searchTerm - Search term
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Search results
   */
  async searchPackages(searchTerm, options = {}) {
    if (!searchTerm) {
      throw new ValidationError('searchTerm is required');
    }

    return this.getPackages({
      ...options,
      sorting: `name:${searchTerm}`
    });
  }

  /**
   * Get packages by price range
   * @param {number} minPrice - Minimum price
   * @param {number} maxPrice - Maximum price
   * @param {Object} options - Additional options
   * @returns {Promise<Array<Object>>} Filtered packages
   */
  async getPackagesByPriceRange(minPrice, maxPrice, options = {}) {
    const allPackages = await this.getPackages(options);
    
    if (!allPackages.items) {
      return [];
    }

    return allPackages.items.filter(pkg => 
      pkg.price >= minPrice && pkg.price <= maxPrice
    );
  }

  /**
   * Get unlimited packages
   * @param {Object} options - Additional options
   * @returns {Promise<Array<Object>>} Unlimited packages
   */
  async getUnlimitedPackages(options = {}) {
    const allPackages = await this.getPackages(options);
    
    if (!allPackages.items) {
      return [];
    }

    return allPackages.items.filter(pkg => pkg.unlimited === true);
  }

  /**
   * Validate package data
   * @param {Object} packageData - Package data to validate
   * @returns {Object} Validation result with isValid boolean and errors array
   */
  validatePackageData(packageData) {
    const errors = [];

    if (!packageData.name) {
      errors.push('name is required');
    } else if (packageData.name.length > 100) {
      errors.push('name must not exceed 100 characters');
    }

    if (packageData.quantity === undefined || packageData.quantity === null) {
      errors.push('quantity is required');
    } else if (typeof packageData.quantity !== 'number') {
      errors.push('quantity must be a number');
    } else if (packageData.quantity < 0) {
      errors.push('quantity must be non-negative');
    }

    if (packageData.price === undefined || packageData.price === null) {
      errors.push('price is required');
    } else if (typeof packageData.price !== 'number') {
      errors.push('price must be a number');
    } else if (packageData.price < 0) {
      errors.push('price must be non-negative');
    }

    if (!packageData.features) {
      errors.push('features is required');
    } else if (packageData.features.length > 500) {
      errors.push('features must not exceed 500 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export default PackagesAPI;
