/**
 * Utility functions for the Neosending client
 */

/**
 * Validate phone number format
 * @param {string} phoneNumber - Phone number to validate
 * @returns {boolean} True if valid phone number format
 */
export function isValidPhoneNumber(phoneNumber) {
  if (!phoneNumber || typeof phoneNumber !== 'string') {
    return false;
  }
  
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Check if it's at least 8 digits (as per API requirement)
  return cleaned.length >= 8 && cleaned.length <= 15;
}

/**
 * Format phone number for API usage
 * @param {string} phoneNumber - Phone number to format
 * @returns {string} Formatted phone number
 */
export function formatPhoneNumber(phoneNumber) {
  if (!phoneNumber) return '';
  
  // Remove all non-digit characters except +
  let formatted = phoneNumber.replace(/[^\d+]/g, '');
  
  // Ensure it starts with + if it doesn't already
  if (!formatted.startsWith('+')) {
    formatted = '+' + formatted;
  }
  
  return formatted;
}

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid email format
 */
export function isValidEmail(email) {
  if (!email || typeof email !== 'string') {
    return false;
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 256;
}

/**
 * Convert file to base64 string
 * @param {File|Buffer} file - File to convert
 * @returns {Promise<string>} Base64 string
 */
export async function fileToBase64(file) {
  if (Buffer.isBuffer(file)) {
    return file.toString('base64');
  }
  
  if (file instanceof File || file.constructor.name === 'File') {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result.split(',')[1]; // Remove data:type;base64, prefix
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }
  
  throw new Error('Unsupported file type');
}

/**
 * Retry function with exponential backoff
 * @param {Function} fn - Function to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise<any>} Result of the function
 */
export async function retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000) {
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // Don't retry on certain error types
      if (error.statusCode === 400 || error.statusCode === 401 || error.statusCode === 403) {
        throw error;
      }
      
      if (attempt < maxRetries) {
        const delay = baseDelay * Math.pow(2, attempt);
        await sleep(delay);
      }
    }
  }
  
  throw lastError;
}

/**
 * Sleep for specified milliseconds
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise<void>}
 */
export function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Validate pagination parameters
 * @param {Object} options - Pagination options
 * @returns {Object} Validated pagination options
 */
export function validatePaginationOptions(options = {}) {
  const validated = {};
  
  if (options.skipCount !== undefined) {
    if (typeof options.skipCount !== 'number' || options.skipCount < 0) {
      throw new Error('skipCount must be a non-negative number');
    }
    validated.skipCount = Math.floor(options.skipCount);
  }
  
  if (options.maxResultCount !== undefined) {
    if (typeof options.maxResultCount !== 'number' || options.maxResultCount < 1) {
      throw new Error('maxResultCount must be a positive number');
    }
    validated.maxResultCount = Math.min(Math.floor(options.maxResultCount), 1000); // Cap at 1000
  }
  
  if (options.sorting !== undefined) {
    if (typeof options.sorting !== 'string') {
      throw new Error('sorting must be a string');
    }
    validated.sorting = options.sorting;
  }
  
  return validated;
}

/**
 * Deep clone an object
 * @param {any} obj - Object to clone
 * @returns {any} Cloned object
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item));
  }
  
  if (typeof obj === 'object') {
    const cloned = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
}

/**
 * Sanitize string for API usage
 * @param {string} str - String to sanitize
 * @param {number} maxLength - Maximum length
 * @returns {string} Sanitized string
 */
export function sanitizeString(str, maxLength = null) {
  if (!str || typeof str !== 'string') {
    return '';
  }
  
  // Remove control characters and trim
  let sanitized = str.replace(/[\x00-\x1F\x7F]/g, '').trim();
  
  // Truncate if necessary
  if (maxLength && sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength);
  }
  
  return sanitized;
}

/**
 * Check if a value is empty (null, undefined, empty string, empty array, empty object)
 * @param {any} value - Value to check
 * @returns {boolean} True if empty
 */
export function isEmpty(value) {
  if (value === null || value === undefined) {
    return true;
  }
  
  if (typeof value === 'string') {
    return value.trim().length === 0;
  }
  
  if (Array.isArray(value)) {
    return value.length === 0;
  }
  
  if (typeof value === 'object') {
    return Object.keys(value).length === 0;
  }
  
  return false;
}

/**
 * Generate a random reference ID
 * @param {number} length - Length of the ID
 * @returns {string} Random reference ID
 */
export function generateReferenceId(length = 16) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Parse error response from API
 * @param {Object} errorResponse - Error response from API
 * @returns {string} Parsed error message
 */
export function parseErrorMessage(errorResponse) {
  if (!errorResponse) {
    return 'Unknown error occurred';
  }
  
  // Check for ABP framework error structure
  if (errorResponse.error) {
    if (errorResponse.error.message) {
      return errorResponse.error.message;
    }
    if (errorResponse.error.details) {
      return errorResponse.error.details;
    }
  }
  
  // Check for validation errors
  if (errorResponse.error?.validationErrors?.length > 0) {
    return errorResponse.error.validationErrors
      .map(err => err.message)
      .join(', ');
  }
  
  // Fallback to generic message
  if (typeof errorResponse === 'string') {
    return errorResponse;
  }
  
  return 'An error occurred while processing your request';
}
