import { AuthenticationError } from '../errors/index.js';

/**
 * Manages OAuth2 authentication for the Neosending API
 */
class AuthManager {
  constructor(client) {
    this.client = client;
    this.token = null;
    this.tokenExpiry = null;
    this.refreshPromise = null;
  }

  /**
   * Get a valid access token, refreshing if necessary
   * @returns {Promise<string|null>} Access token or null if not authenticated
   */
  async getValidToken() {
    // If we have a valid token, return it
    if (this.token && this.tokenExpiry && Date.now() < this.tokenExpiry) {
      return this.token;
    }

    // If we're already refreshing, wait for that to complete
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    // Refresh the token
    return this.refreshToken();
  }

  /**
   * Refresh the access token using password flow
   * @returns {Promise<string>} New access token
   */
  async refreshToken() {
    // Prevent multiple simultaneous refresh attempts
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this._performTokenRefresh();
    
    try {
      const token = await this.refreshPromise;
      return token;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * Perform the actual token refresh
   * @private
   * @returns {Promise<string>} New access token
   */
  async _performTokenRefresh() {
    try {
      const response = await this.client.http.post('/connect/token', 
        new URLSearchParams({
          grant_type: 'password',
          username: this.client.config.username,
          password: this.client.config.password,
          scope: 'Whatsapp'
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      const { access_token, expires_in } = response.data;
      
      if (!access_token) {
        throw new AuthenticationError('No access token received from server');
      }

      // Store token and calculate expiry (subtract 5 minutes for safety)
      this.token = access_token;
      this.tokenExpiry = Date.now() + ((expires_in - 300) * 1000);

      return this.token;
    } catch (error) {
      this.token = null;
      this.tokenExpiry = null;

      if (error.response?.status === 400 || error.response?.status === 401) {
        throw new AuthenticationError(
          'Invalid credentials or authentication failed',
          error.response.data
        );
      }

      throw new AuthenticationError(
        'Authentication request failed',
        error.response?.data || error.message
      );
    }
  }

  /**
   * Check if currently authenticated
   * @returns {boolean} True if authenticated with valid token
   */
  isAuthenticated() {
    return this.token && this.tokenExpiry && Date.now() < this.tokenExpiry;
  }

  /**
   * Clear stored authentication data
   */
  clearAuth() {
    this.token = null;
    this.tokenExpiry = null;
    this.refreshPromise = null;
  }

  /**
   * Get current token info (for debugging)
   * @returns {Object} Token information
   */
  getTokenInfo() {
    return {
      hasToken: !!this.token,
      isExpired: this.tokenExpiry ? Date.now() >= this.tokenExpiry : true,
      expiresAt: this.tokenExpiry ? new Date(this.tokenExpiry).toISOString() : null,
      timeUntilExpiry: this.tokenExpiry ? Math.max(0, this.tokenExpiry - Date.now()) : 0
    };
  }
}

export default AuthManager;
