# Neosending WhatsApp API Client

A comprehensive JavaScript client for the Neosending WhatsApp API, providing easy-to-use methods for sending messages, managing customers, handling sessions, and more.

## Features

- 🚀 **Easy to use** - Simple, intuitive API
- 🔐 **Secure authentication** - OAuth2 password flow with automatic token refresh
- 📱 **Complete WhatsApp functionality** - Send text, files, images, vCards
- 👥 **Customer management** - Full CRUD operations for customers
- 📦 **Package & subscription management** - Handle billing and subscriptions
- 🔄 **Session management** - Start, stop, and monitor WhatsApp sessions
- 📊 **Mobile account management** - Manage multiple WhatsApp accounts
- ⚡ **Built-in retry logic** - Automatic retry with exponential backoff
- 🛡️ **Comprehensive error handling** - Detailed error types and messages
- 📝 **TypeScript-ready** - Full type definitions included

## Installation

```bash
npm install neosending-client
```

## Quick Start

```javascript
import NeosendingClient from 'neosending-client';

// Initialize the client
const client = new NeosendingClient({
  username: 'your-username',
  password: 'your-password',
  baseURL: 'https://neosending.com' // optional, defaults to https://neosending.com
});

// Send a text message
try {
  const result = await client.messages.sendTextMessage({
    receiverMobile: '+**********',
    content: 'Hello from Neosending!'
  });
  console.log('Message sent:', result);
} catch (error) {
  console.error('Failed to send message:', error.message);
}
```

## Configuration

### Basic Configuration

```javascript
const client = new NeosendingClient({
  username: 'your-username',
  password: 'your-password',
  baseURL: 'https://neosending.com', // optional
  timeout: 30000, // optional, request timeout in ms
  headers: { // optional, additional headers
    'Custom-Header': 'value'
  }
});
```

### Environment Variables

You can also configure using environment variables:

```bash
NEOSENDING_USERNAME=your-username
NEOSENDING_PASSWORD=your-password
NEOSENDING_BASE_URL=https://neosending.com
NEOSENDING_TIMEOUT=30000
```

```javascript
import { createConfigFromEnv } from 'neosending-client/config';

const client = new NeosendingClient(createConfigFromEnv());
```

## API Reference

### Messages API

#### Send Text Message

```javascript
const result = await client.messages.sendTextMessage({
  receiverMobile: '+**********',
  content: 'Your message here',
  senderMobile: '+**********', // optional
  priority: 2, // optional, 1-4
  referenceId: 'unique-ref-id' // optional
});
```

#### Send File Message

```javascript
// From file path or buffer
const result = await client.messages.sendFileMessage({
  receiverMobile: '+**********',
  content: fileBuffer, // Buffer, base64 string, or File object
  fileName: 'document.pdf',
  caption: 'Here is your document', // optional
  priority: 3
});
```

#### Send vCard (Contact)

```javascript
const result = await client.messages.sendVCard({
  receiverMobile: '+**********',
  contactName: 'John Doe',
  contactId: '+1111111111'
});
```

#### Batch Messages

```javascript
const messages = [
  client.messages.createTextMessage('+**********', 'Hello!'),
  client.messages.createFileMessage('+**********', fileBuffer, 'image.jpg'),
  client.messages.createVCardMessage('+**********', 'John', '+1111111111')
];

const results = await client.messages.sendBatchMessages(messages);
```

### Customer API

#### Get Customer Information

```javascript
// Get current WhatsApp customer
const customer = await client.customers.getWhatsappCustomer();

// Get customer by ID
const customer = await client.customers.getCustomer(123);

// Get customer balance
const balance = await client.customers.getCustomerBalance();
```

#### Create Customer

```javascript
const newCustomer = await client.customers.createCustomer({
  userName: 'johndoe',
  password: 'securepassword',
  mobileNumber: '+**********',
  fullName: 'John Doe', // optional
  masterMobileNumber: '+**********' // optional
});
```

#### Update Customer

```javascript
const updatedCustomer = await client.customers.updateCustomer(123, {
  companyName: 'Acme Corp',
  mobileNumber: '+**********',
  fullName: 'John Doe Updated'
});
```

### Session Management

#### Start WhatsApp Session

```javascript
// Start session and get QR code
const session = await client.sessions.startSessionAndGetQR(
  '+**********',
  'https://your-webhook-url.com/webhook' // optional
);

console.log('QR Code:', session.qrcode);
```

#### Monitor Session Status

```javascript
// Check session status
const status = await client.sessions.getSessionStatus('+**********');
console.log('Session status:', status.status);

// Wait for session to be ready
try {
  const readyStatus = await client.sessions.waitForSessionReady('+**********');
  console.log('Session is ready!');
} catch (error) {
  console.error('Session failed to connect:', error.message);
}
```

#### Session Operations

```javascript
// Take screenshot
const screenshot = await client.sessions.takeScreenshot('+**********');
console.log('Screenshot base64:', screenshot.base64);

// Get WhatsApp groups
const groups = await client.sessions.getWhatsappGroups('+**********');
console.log('Groups:', groups.response);

// Logout session
await client.sessions.logoutSession('+**********');

// Close session
await client.sessions.closeSession('+**********');
```

### Mobile Accounts API

#### Manage Mobile Accounts

```javascript
// Get all mobile accounts
const accounts = await client.mobileAccounts.getMobileAccounts();

// Create new mobile account
const newAccount = await client.mobileAccounts.createMobileAccount(
  123, // customerId
  '+**********', // mobileNumber
  1 // clientType (1, 2, or 3)
);

// Update mobile account
const updated = await client.mobileAccounts.updateMobileAccount(456, {
  mobileNumber: '+**********',
  clientType: 2
});
```

### Packages & Subscriptions

#### Package Management

```javascript
// Get all packages
const packages = await client.packages.getPackages();

// Create package
const newPackage = await client.packages.createPackage({
  name: 'Premium Package',
  quantity: 1000,
  price: 99.99,
  features: 'Unlimited messages, Priority support',
  unlimited: false
});

// Get packages by price range
const affordablePackages = await client.packages.getPackagesByPriceRange(0, 50);
```

#### Subscription Management

```javascript
// Create subscription
const subscription = await client.subscriptions.createSubscription(
  123, // customerId
  456, // packageId
  1    // periodType: 1=Monthly, 2=Quarterly, 3=Semi-Annual, 4=Annual
);

// Get customer's current package
const currentPackage = await client.subscriptions.getCurrentPackage(123);
```

## Error Handling

The client provides detailed error types for better error handling:

```javascript
import { 
  NeosendingError, 
  AuthenticationError, 
  ValidationError, 
  NetworkError 
} from 'neosending-client';

try {
  await client.messages.sendTextMessage({
    receiverMobile: 'invalid-number',
    content: 'Hello'
  });
} catch (error) {
  if (error instanceof ValidationError) {
    console.error('Validation error:', error.message);
  } else if (error instanceof AuthenticationError) {
    console.error('Authentication failed:', error.message);
  } else if (error instanceof NetworkError) {
    console.error('Network error:', error.message);
  } else {
    console.error('General error:', error.message);
  }
}
```

## Utilities

The client includes helpful utility functions:

```javascript
import { 
  isValidPhoneNumber, 
  formatPhoneNumber, 
  fileToBase64,
  generateReferenceId 
} from 'neosending-client/utils';

// Validate phone number
if (isValidPhoneNumber('+**********')) {
  console.log('Valid phone number');
}

// Format phone number
const formatted = formatPhoneNumber('**********'); // Returns '+**********'

// Convert file to base64
const base64 = await fileToBase64(fileBuffer);

// Generate reference ID
const refId = generateReferenceId(16);
```

## Advanced Usage

### Custom Request Configuration

```javascript
// Access the underlying axios instance for custom requests
const response = await client.http.get('/custom-endpoint');
```

### Authentication Management

```javascript
// Check authentication status
const isAuth = await client.isAuthenticated();

// Manually refresh token
await client.refreshAuth();

// Get token information (for debugging)
const tokenInfo = client.auth.getTokenInfo();
console.log('Token expires at:', tokenInfo.expiresAt);
```

## License

MIT

## Support

For support and questions, please contact the Neosending team or create an issue in the repository.
