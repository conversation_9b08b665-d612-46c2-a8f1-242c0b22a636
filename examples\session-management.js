import NeosendingClient from '../src/index.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Session management example
 */
async function sessionManagementExample() {
  try {
    // Initialize the client
    const client = new NeosendingClient({
      username: process.env.NEOSENDING_USERNAME || 'your-username',
      password: process.env.NEOSENDING_PASSWORD || 'your-password'
    });

    console.log('🔄 Neosending Session Management Example');
    console.log('========================================\n');

    // Replace with actual mobile number for testing
    const testMobile = process.env.TEST_MOBILE_NUMBER || '+1234567890';
    const webhookUrl = process.env.WEBHOOK_URL || 'https://your-webhook.com/whatsapp';

    console.log(`📱 Test mobile: ${testMobile}`);
    console.log(`🔗 Webhook URL: ${webhookUrl}\n`);

    // 1. Start a new session
    console.log('1️⃣ Starting WhatsApp session...');
    try {
      const sessionResult = await client.sessions.startSession({
        mobileNumber: testMobile,
        webhook: webhookUrl,
        waitQrCode: true
      });

      console.log('✅ Session start result:', sessionResult.status);
      
      if (sessionResult.qrcode) {
        console.log('📱 QR Code available for scanning');
        console.log('   QR Code data length:', sessionResult.qrcode.length);
        // In a real application, you would display this QR code to the user
        // or save it as an image file for scanning
      }
    } catch (error) {
      console.log('❌ Failed to start session:', error.message);
    }

    // 2. Check session status
    console.log('\n2️⃣ Checking session status...');
    try {
      const status = await client.sessions.getSessionStatus(testMobile);
      console.log('📊 Session status:', {
        status: status.status,
        message: status.message,
        version: status.version
      });

      // If QR code is needed, get it
      if (status.status === 'QRCODE' || status.qrcode) {
        console.log('📱 QR Code is available in status response');
      }
    } catch (error) {
      console.log('❌ Failed to get session status:', error.message);
    }

    // 3. Get QR code separately (if needed)
    console.log('\n3️⃣ Getting QR code...');
    try {
      const qrResult = await client.sessions.getQRCode(testMobile);
      console.log('📱 QR Code result:', {
        status: qrResult.status,
        hasImage: !!qrResult.image,
        hasContent: !!qrResult.content,
        lastStatus: qrResult.lastStatus
      });
    } catch (error) {
      console.log('❌ Failed to get QR code:', error.message);
    }

    // 4. Check connection status
    console.log('\n4️⃣ Checking connection...');
    try {
      const connectionStatus = await client.sessions.checkConnection(testMobile);
      console.log('🔗 Connection status:', {
        status: connectionStatus.status,
        message: connectionStatus.message
      });
    } catch (error) {
      console.log('❌ Failed to check connection:', error.message);
    }

    // 5. Wait for session to be ready (with timeout)
    console.log('\n5️⃣ Waiting for session to be ready...');
    try {
      console.log('⏳ Waiting up to 60 seconds for session to connect...');
      console.log('   (In a real app, user would scan QR code during this time)');
      
      // Wait for session with shorter timeout for demo
      const readyStatus = await client.sessions.waitForSessionReady(
        testMobile,
        6, // max attempts
        10000 // 10 seconds between checks
      );
      
      console.log('✅ Session is ready!', readyStatus.status);
    } catch (error) {
      console.log('⏰ Session did not become ready within timeout period');
      console.log('   This is normal for demo - user needs to scan QR code');
    }

    // 6. Take a screenshot (if session is active)
    console.log('\n6️⃣ Taking session screenshot...');
    try {
      const screenshot = await client.sessions.takeScreenshot(testMobile);
      console.log('📸 Screenshot result:', {
        success: screenshot.success,
        message: screenshot.message,
        hasBase64: !!screenshot.base64
      });

      if (screenshot.base64) {
        console.log('   Screenshot data length:', screenshot.base64.length);
        // In a real app, you could save this as an image file
      }
    } catch (error) {
      console.log('❌ Failed to take screenshot:', error.message);
    }

    // 7. Get WhatsApp groups (if session is connected)
    console.log('\n7️⃣ Getting WhatsApp groups...');
    try {
      const groups = await client.sessions.getWhatsappGroups(testMobile);
      console.log('👥 Groups result:', {
        status: groups.status,
        groupCount: groups.response ? groups.response.length : 0,
        message: groups.message
      });

      if (groups.response && groups.response.length > 0) {
        console.log('   Groups found:');
        groups.response.slice(0, 3).forEach((group, index) => {
          console.log(`     ${index + 1}. ${group.name} (${group.id})`);
        });
        if (groups.response.length > 3) {
          console.log(`     ... and ${groups.response.length - 3} more`);
        }
      }
    } catch (error) {
      console.log('❌ Failed to get groups:', error.message);
    }

    // 8. Start Node.js server (if needed)
    console.log('\n8️⃣ Starting Node.js server...');
    try {
      const serverResult = await client.sessions.startNodeJsServer({
        mobileNumber: testMobile
      });
      console.log('🖥️ Server start result:', {
        status: serverResult.status,
        message: serverResult.message
      });
    } catch (error) {
      console.log('❌ Failed to start Node.js server:', error.message);
    }

    // 9. Generate WppConnect token
    console.log('\n9️⃣ Generating WppConnect token...');
    try {
      const tokenResult = await client.sessions.generateWppConnectToken(testMobile);
      console.log('🔑 Token generation result:', {
        status: tokenResult.status,
        session: tokenResult.session,
        hasToken: !!tokenResult.token
      });
    } catch (error) {
      console.log('❌ Failed to generate token:', error.message);
    }

    // 10. Session cleanup operations
    console.log('\n🔟 Session cleanup operations...');
    
    // Logout session
    try {
      const logoutResult = await client.sessions.logoutSession(testMobile);
      console.log('👋 Logout result:', {
        response: logoutResult.response,
        status: logoutResult.status,
        message: logoutResult.message
      });
    } catch (error) {
      console.log('❌ Failed to logout session:', error.message);
    }

    // Close session
    try {
      const closeResult = await client.sessions.closeSession(testMobile);
      console.log('🔒 Close session result:', {
        status: closeResult.status,
        message: closeResult.message
      });
    } catch (error) {
      console.log('❌ Failed to close session:', error.message);
    }

    // Delete session
    try {
      const deleteResult = await client.sessions.deleteSession({
        mobileNumber: testMobile
      });
      console.log('🗑️ Delete session result:', deleteResult);
    } catch (error) {
      console.log('❌ Failed to delete session:', error.message);
    }

    console.log('\n🎉 Session management example completed!');
    console.log('\n💡 Session Management Tips:');
    console.log('   - Always start with session creation and QR code display');
    console.log('   - Monitor session status regularly');
    console.log('   - Handle QR code expiration and regeneration');
    console.log('   - Implement proper session cleanup');
    console.log('   - Use webhooks for real-time session events');
    console.log('   - Take screenshots for debugging session issues');

  } catch (error) {
    console.error('❌ Error in session management example:', error.message);
    if (error.statusCode) {
      console.error(`Status Code: ${error.statusCode}`);
    }
  }
}

// Run the example
if (import.meta.url === `file://${process.argv[1]}`) {
  sessionManagementExample();
}

export default sessionManagementExample;
