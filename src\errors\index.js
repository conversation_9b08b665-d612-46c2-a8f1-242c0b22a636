/**
 * Base error class for Neosending API errors
 */
export class NeosendingError extends Error {
  constructor(message, statusCode = null, data = null) {
    super(message);
    this.name = 'NeosendingError';
    this.statusCode = statusCode;
    this.data = data;
    
    // Maintain proper stack trace for where our error was thrown
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, NeosendingError);
    }
  }

  /**
   * Get error details as a plain object
   * @returns {Object} Error details
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      statusCode: this.statusCode,
      data: this.data,
      stack: this.stack
    };
  }
}

/**
 * Authentication-related errors
 */
export class AuthenticationError extends NeosendingError {
  constructor(message, data = null) {
    super(message, 401, data);
    this.name = 'AuthenticationError';
  }
}

/**
 * Validation-related errors
 */
export class ValidationError extends NeosendingError {
  constructor(message, data = null) {
    super(message, 400, data);
    this.name = 'ValidationError';
  }
}

/**
 * Network-related errors
 */
export class NetworkError extends NeosendingError {
  constructor(message, originalError = null) {
    super(message, null, originalError);
    this.name = 'NetworkError';
    this.originalError = originalError;
  }
}

/**
 * Rate limiting errors
 */
export class RateLimitError extends NeosendingError {
  constructor(message, retryAfter = null) {
    super(message, 429);
    this.name = 'RateLimitError';
    this.retryAfter = retryAfter;
  }
}

/**
 * Resource not found errors
 */
export class NotFoundError extends NeosendingError {
  constructor(message, data = null) {
    super(message, 404, data);
    this.name = 'NotFoundError';
  }
}

/**
 * Server errors
 */
export class ServerError extends NeosendingError {
  constructor(message, statusCode = 500, data = null) {
    super(message, statusCode, data);
    this.name = 'ServerError';
  }
}
