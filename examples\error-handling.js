import NeosendingClient from '../src/index.js';
import { 
  NeosendingError, 
  AuthenticationError, 
  ValidationError, 
  NetworkError 
} from '../src/errors/index.js';
import { retryWithBackoff } from '../src/utils/index.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Error handling and retry logic example
 */
async function errorHandlingExample() {
  console.log('🛡️ Neosending Error Handling Example');
  console.log('====================================\n');

  // 1. Authentication Error Example
  console.log('1️⃣ Testing authentication error...');
  try {
    const badClient = new NeosendingClient({
      username: 'invalid-username',
      password: 'invalid-password'
    });

    await badClient.customers.getWhatsappCustomer();
  } catch (error) {
    if (error instanceof AuthenticationError) {
      console.log('✅ Caught authentication error correctly:', error.message);
    } else {
      console.log('❌ Unexpected error type:', error.constructor.name);
    }
  }

  // 2. Validation Error Example
  console.log('\n2️⃣ Testing validation error...');
  try {
    const client = new NeosendingClient({
      username: process.env.NEOSENDING_USERNAME || 'test',
      password: process.env.NEOSENDING_PASSWORD || 'test'
    });

    // Try to send message with invalid data
    await client.messages.sendTextMessage({
      receiverMobile: '', // Invalid: empty mobile number
      content: '' // Invalid: empty content
    });
  } catch (error) {
    if (error instanceof ValidationError) {
      console.log('✅ Caught validation error correctly:', error.message);
    } else {
      console.log('❌ Unexpected error type:', error.constructor.name, error.message);
    }
  }

  // 3. Network Error Simulation
  console.log('\n3️⃣ Testing network error...');
  try {
    const client = new NeosendingClient({
      username: process.env.NEOSENDING_USERNAME || 'test',
      password: process.env.NEOSENDING_PASSWORD || 'test',
      baseURL: 'https://invalid-domain-that-does-not-exist.com'
    });

    await client.customers.getWhatsappCustomer();
  } catch (error) {
    if (error instanceof NetworkError) {
      console.log('✅ Caught network error correctly:', error.message);
    } else {
      console.log('❌ Unexpected error type:', error.constructor.name, error.message);
    }
  }

  // 4. Comprehensive Error Handling Pattern
  console.log('\n4️⃣ Comprehensive error handling pattern...');
  
  const client = new NeosendingClient({
    username: process.env.NEOSENDING_USERNAME || 'test',
    password: process.env.NEOSENDING_PASSWORD || 'test'
  });

  async function handleApiCall(operation, operationName) {
    try {
      const result = await operation();
      console.log(`✅ ${operationName} succeeded:`, result?.status || 'OK');
      return result;
    } catch (error) {
      console.log(`❌ ${operationName} failed:`);
      
      if (error instanceof AuthenticationError) {
        console.log('   🔐 Authentication issue - check credentials');
        console.log('   💡 Suggestion: Verify username and password');
      } else if (error instanceof ValidationError) {
        console.log('   📝 Validation issue - check input data');
        console.log('   💡 Suggestion: Review API documentation for required fields');
      } else if (error instanceof NetworkError) {
        console.log('   🌐 Network issue - check connectivity');
        console.log('   💡 Suggestion: Verify internet connection and API endpoint');
      } else if (error instanceof NeosendingError) {
        console.log('   🚨 API error:', error.message);
        if (error.statusCode) {
          console.log('   📊 Status code:', error.statusCode);
        }
        if (error.data) {
          console.log('   📋 Error data:', JSON.stringify(error.data, null, 2));
        }
      } else {
        console.log('   ❓ Unknown error:', error.message);
      }
      
      return null;
    }
  }

  // Test various operations with error handling
  await handleApiCall(
    () => client.customers.getWhatsappCustomer(),
    'Get WhatsApp Customer'
  );

  await handleApiCall(
    () => client.customers.getCustomerBalance(),
    'Get Customer Balance'
  );

  await handleApiCall(
    () => client.mobileAccounts.getMobileAccounts({ maxResultCount: 1 }),
    'Get Mobile Accounts'
  );

  // 5. Retry Logic Example
  console.log('\n5️⃣ Testing retry logic...');
  
  let attemptCount = 0;
  const unreliableOperation = async () => {
    attemptCount++;
    console.log(`   Attempt ${attemptCount}...`);
    
    // Simulate intermittent failures
    if (attemptCount < 3) {
      throw new Error('Simulated temporary failure');
    }
    
    return { success: true, attempt: attemptCount };
  };

  try {
    const result = await retryWithBackoff(
      unreliableOperation,
      3, // max retries
      500 // base delay
    );
    console.log('✅ Retry logic succeeded after', result.attempt, 'attempts');
  } catch (error) {
    console.log('❌ Retry logic failed:', error.message);
  }

  // 6. Error Recovery Patterns
  console.log('\n6️⃣ Error recovery patterns...');

  // Pattern 1: Fallback to default values
  async function getCustomerInfoWithFallback() {
    try {
      return await client.customers.getWhatsappCustomer();
    } catch (error) {
      console.log('   ⚠️ Using fallback customer info due to error:', error.message);
      return {
        id: null,
        fullName: 'Unknown Customer',
        companyName: 'Unknown Company',
        mobileNumber: 'Unknown'
      };
    }
  }

  const customerInfo = await getCustomerInfoWithFallback();
  console.log('✅ Customer info (with fallback):', {
    fullName: customerInfo.fullName,
    companyName: customerInfo.companyName
  });

  // Pattern 2: Graceful degradation
  async function getAccountsWithGracefulDegradation() {
    try {
      const accounts = await client.mobileAccounts.getMobileAccounts();
      return {
        success: true,
        accounts: accounts.items || [],
        totalCount: accounts.totalCount || 0
      };
    } catch (error) {
      console.log('   ⚠️ Could not fetch accounts, continuing with limited functionality');
      return {
        success: false,
        accounts: [],
        totalCount: 0,
        error: error.message
      };
    }
  }

  const accountsResult = await getAccountsWithGracefulDegradation();
  console.log('✅ Accounts result (graceful degradation):', {
    success: accountsResult.success,
    count: accountsResult.totalCount
  });

  // 7. Error Logging and Monitoring
  console.log('\n7️⃣ Error logging and monitoring...');

  function logError(error, context = {}) {
    const errorInfo = {
      timestamp: new Date().toISOString(),
      type: error.constructor.name,
      message: error.message,
      statusCode: error.statusCode || null,
      context: context,
      stack: error.stack
    };

    // In production, you would send this to your logging service
    console.log('📊 Error logged:', JSON.stringify(errorInfo, null, 2));
  }

  try {
    await client.customers.getCustomer(99999); // Non-existent customer
  } catch (error) {
    logError(error, { 
      operation: 'getCustomer', 
      customerId: 99999,
      userId: 'example-user-123'
    });
  }

  console.log('\n🎉 Error handling example completed!');
  console.log('\n💡 Error Handling Best Practices:');
  console.log('   - Always use specific error types for different handling');
  console.log('   - Implement retry logic for transient failures');
  console.log('   - Provide fallback values when appropriate');
  console.log('   - Log errors with context for debugging');
  console.log('   - Use graceful degradation for non-critical features');
  console.log('   - Display user-friendly error messages');
  console.log('   - Monitor error rates and patterns in production');
}

// Run the example
if (import.meta.url === `file://${process.argv[1]}`) {
  errorHandlingExample();
}

export default errorHandlingExample;
