import { ValidationError } from '../errors/index.js';

/**
 * Customer API methods
 */
class CustomerAPI {
  constructor(client) {
    this.client = client;
  }

  /**
   * Get a customer by ID
   * @param {number} id - Customer ID
   * @returns {Promise<Object>} Customer data
   */
  async getCustomer(id) {
    if (!id || typeof id !== 'number') {
      throw new ValidationError('Customer ID is required and must be a number');
    }

    const response = await this.client.http.get(`/api/neosending/Whatsapp/customer/${id}`);
    return response.data;
  }

  /**
   * Get list of customers with pagination and filtering
   * @param {Object} options - Query options
   * @param {string} options.filter - Filter string
   * @param {string} options.sorting - Sorting criteria
   * @param {number} options.skipCount - Number of records to skip
   * @param {number} options.maxResultCount - Maximum number of records to return
   * @returns {Promise<Object>} Paginated customer list
   */
  async getCustomers(options = {}) {
    const params = new URLSearchParams();
    
    if (options.filter) params.append('Filter', options.filter);
    if (options.sorting) params.append('Sorting', options.sorting);
    if (options.skipCount !== undefined) params.append('SkipCount', options.skipCount);
    if (options.maxResultCount !== undefined) params.append('MaxResultCount', options.maxResultCount);

    const response = await this.client.http.get(`/api/neosending/Whatsapp/customer?${params}`);
    return response.data;
  }

  /**
   * Create a new customer
   * @param {Object} customerData - Customer data
   * @param {string} customerData.userName - Username (required)
   * @param {string} customerData.password - Password (required)
   * @param {string} customerData.mobileNumber - Mobile number (required, min 8 chars)
   * @param {string} customerData.fullName - Full name (optional)
   * @param {string} customerData.masterMobileNumber - Master mobile number (optional)
   * @returns {Promise<Object>} Created customer data
   */
  async createCustomer(customerData) {
    // Validate required fields
    if (!customerData.userName) {
      throw new ValidationError('userName is required');
    }
    if (!customerData.password) {
      throw new ValidationError('password is required');
    }
    if (!customerData.mobileNumber || customerData.mobileNumber.length < 8) {
      throw new ValidationError('mobileNumber is required and must be at least 8 characters');
    }

    const response = await this.client.http.post('/api/neosending/Whatsapp/customer', customerData);
    return response.data;
  }

  /**
   * Update an existing customer
   * @param {number} id - Customer ID
   * @param {Object} customerData - Updated customer data
   * @param {string} customerData.companyName - Company name (required)
   * @param {string} customerData.mobileNumber - Mobile number (required, min 8 chars)
   * @param {string} customerData.fullName - Full name (optional)
   * @param {string} customerData.masterMobileNumber - Master mobile number (optional)
   * @returns {Promise<Object>} Updated customer data
   */
  async updateCustomer(id, customerData) {
    if (!id || typeof id !== 'number') {
      throw new ValidationError('Customer ID is required and must be a number');
    }

    // Validate required fields
    if (!customerData.companyName) {
      throw new ValidationError('companyName is required');
    }
    if (!customerData.mobileNumber || customerData.mobileNumber.length < 8) {
      throw new ValidationError('mobileNumber is required and must be at least 8 characters');
    }

    const response = await this.client.http.put(`/api/neosending/Whatsapp/customer/${id}`, customerData);
    return response.data;
  }

  /**
   * Delete a customer
   * @param {number} id - Customer ID
   * @returns {Promise<void>}
   */
  async deleteCustomer(id) {
    if (!id || typeof id !== 'number') {
      throw new ValidationError('Customer ID is required and must be a number');
    }

    await this.client.http.delete(`/api/neosending/Whatsapp/customer/${id}`);
  }

  /**
   * Get current WhatsApp customer information
   * @returns {Promise<Object>} WhatsApp customer info
   */
  async getWhatsappCustomer() {
    const response = await this.client.http.get('/api/neosending/Whatsapp/customer/whatsapp-customer');
    return response.data;
  }

  /**
   * Get customer balance information
   * @returns {Promise<Object>} Customer balance data
   */
  async getCustomerBalance() {
    const response = await this.client.http.get('/api/neosending/Whatsapp/customer/customer-balance');
    return response.data;
  }

  /**
   * Save BON (Business Operations Network) settings
   * @param {Object} settings - WhatsApp general settings
   * @param {string} settings.whatsappSettings - WhatsApp settings JSON string
   * @returns {Promise<Object>} HTTP status code response
   */
  async saveBonSettings(settings) {
    if (!settings.whatsappSettings) {
      throw new ValidationError('whatsappSettings is required');
    }

    const response = await this.client.http.post('/api/neosending/Whatsapp/customer/save-bon-settings', settings);
    return response.data;
  }
}

export default CustomerAPI;
