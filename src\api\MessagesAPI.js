import FormData from 'form-data';
import { ValidationError } from '../errors/index.js';

/**
 * Messages API methods for sending various types of WhatsApp messages
 */
class MessagesAPI {
  constructor(client) {
    this.client = client;
  }

  /**
   * Send a text message
   * @param {Object} messageData - Message data
   * @param {string} messageData.senderMobile - Sender mobile number
   * @param {string} messageData.receiverMobile - Receiver mobile number
   * @param {string} messageData.content - Message content
   * @param {number} messageData.priority - Message priority (optional)
   * @param {string} messageData.whatsappReplyMessageId - Reply to message ID (optional)
   * @param {string} messageData.referenceId - Reference ID (optional)
   * @returns {Promise<Object>} Send message response
   */
  async sendTextMessage(messageData) {
    // Validate required fields
    if (!messageData.receiverMobile) {
      throw new ValidationError('receiverMobile is required');
    }
    if (!messageData.content) {
      throw new ValidationError('content is required');
    }

    const response = await this.client.http.post('/api/neosending/Whatsapp/message/send-text', messageData);
    return response.data;
  }

  /**
   * Send a file message (document, image, video, audio)
   * @param {Object} messageData - Message data
   * @param {string} messageData.senderMobile - Sender mobile number
   * @param {string} messageData.receiverMobile - Receiver mobile number
   * @param {string|Buffer|File} messageData.content - File content (base64 string, Buffer, or File object)
   * @param {string} messageData.fileName - File name (required)
   * @param {string} messageData.caption - File caption (optional)
   * @param {number} messageData.priority - Message priority (optional)
   * @param {string} messageData.whatsappReplyMessageId - Reply to message ID (optional)
   * @param {string} messageData.referenceId - Reference ID (optional)
   * @returns {Promise<Object>} Send message response
   */
  async sendFileMessage(messageData) {
    // Validate required fields
    if (!messageData.receiverMobile) {
      throw new ValidationError('receiverMobile is required');
    }
    if (!messageData.content) {
      throw new ValidationError('content is required');
    }
    if (!messageData.fileName) {
      throw new ValidationError('fileName is required');
    }

    // Handle different content types
    let processedData = { ...messageData };
    
    // If content is a Buffer or File, convert to base64
    if (Buffer.isBuffer(messageData.content)) {
      processedData.content = messageData.content.toString('base64');
    } else if (messageData.content instanceof File || messageData.content.constructor.name === 'File') {
      // For File objects, we need to read the content
      const arrayBuffer = await messageData.content.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      processedData.content = buffer.toString('base64');
    }

    const response = await this.client.http.post('/api/neosending/Whatsapp/message/send-file', processedData);
    return response.data;
  }

  /**
   * Send a templated image message
   * @param {Object} messageData - Message data
   * @param {string} messageData.senderMobile - Sender mobile number
   * @param {string} messageData.receiverMobile - Receiver mobile number
   * @param {string} messageData.templateContent - Template content (optional)
   * @param {string} messageData.caption - Image caption (optional)
   * @param {string} messageData.fileName - File name (optional)
   * @param {number} messageData.priority - Message priority (optional)
   * @param {string} messageData.whatsappReplyMessageId - Reply to message ID (optional)
   * @param {string} messageData.referenceId - Reference ID (optional)
   * @returns {Promise<Object>} Send message response
   */
  async sendTemplatedImageMessage(messageData) {
    if (!messageData.receiverMobile) {
      throw new ValidationError('receiverMobile is required');
    }

    const response = await this.client.http.post('/api/neosending/Whatsapp/message/send-templated-image', messageData);
    return response.data;
  }

  /**
   * Send a vCard (contact card)
   * @param {Object} messageData - Message data
   * @param {string} messageData.senderMobile - Sender mobile number
   * @param {string} messageData.receiverMobile - Receiver mobile number
   * @param {boolean} messageData.isGroup - Whether sending to a group (optional)
   * @param {string} messageData.contactName - Contact name (optional)
   * @param {string} messageData.contactId - Contact ID (optional)
   * @returns {Promise<Object>} Send message response
   */
  async sendVCard(messageData) {
    if (!messageData.receiverMobile) {
      throw new ValidationError('receiverMobile is required');
    }

    const response = await this.client.http.post('/api/neosending/Whatsapp/message/send-vcard', messageData);
    return response.data;
  }

  /**
   * Send multiple messages in batch
   * @param {Array<Object>} messages - Array of message objects
   * @returns {Promise<Array<Object>>} Array of send message responses
   */
  async sendBatchMessages(messages) {
    if (!Array.isArray(messages) || messages.length === 0) {
      throw new ValidationError('messages must be a non-empty array');
    }

    const promises = messages.map(async (message) => {
      try {
        // Determine message type and call appropriate method
        if (message.fileName || message.content) {
          return await this.sendFileMessage(message);
        } else if (message.contactName || message.contactId) {
          return await this.sendVCard(message);
        } else if (message.templateContent) {
          return await this.sendTemplatedImageMessage(message);
        } else {
          return await this.sendTextMessage(message);
        }
      } catch (error) {
        return {
          error: true,
          message: error.message,
          originalMessage: message
        };
      }
    });

    return Promise.all(promises);
  }

  /**
   * Helper method to create a text message object
   * @param {string} to - Receiver mobile number
   * @param {string} text - Message text
   * @param {Object} options - Additional options
   * @returns {Object} Message object
   */
  createTextMessage(to, text, options = {}) {
    return {
      receiverMobile: to,
      content: text,
      ...options
    };
  }

  /**
   * Helper method to create a file message object
   * @param {string} to - Receiver mobile number
   * @param {string|Buffer|File} file - File content
   * @param {string} fileName - File name
   * @param {Object} options - Additional options
   * @returns {Object} Message object
   */
  createFileMessage(to, file, fileName, options = {}) {
    return {
      receiverMobile: to,
      content: file,
      fileName: fileName,
      ...options
    };
  }

  /**
   * Helper method to create a vCard message object
   * @param {string} to - Receiver mobile number
   * @param {string} contactName - Contact name
   * @param {string} contactId - Contact ID
   * @param {Object} options - Additional options
   * @returns {Object} Message object
   */
  createVCardMessage(to, contactName, contactId, options = {}) {
    return {
      receiverMobile: to,
      contactName: contactName,
      contactId: contactId,
      ...options
    };
  }
}

export default MessagesAPI;
